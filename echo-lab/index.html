<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/icons/logo-128.png" type="image/png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta
      name="description"
      content="Echo Lab 是一个高效的精听学习工具，帮助你通过重复听力快速提升语言理解力。"
    />
    <meta
      name="keywords"
      content="日语,精听,听力训练,语言学习,日本语,N2,N3,N4,N5"
    />

    <!-- PWA支持 -->
    <meta name="theme-color" content="#ff0000" />
    <link rel="manifest" href="/manifest.webmanifest" />

    <!-- 移动设备支持 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <!-- iOS支持 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="Echo Lab" />
    <link rel="apple-touch-icon" href="/icons/logo-192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/logo-256.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/logo-192.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/logo-256.png" />
    <title>Echo Lab - 日语精听工具</title>

    <style>
      html {
        font-size: 16px;
      }
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
      }
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #f8fafc;
        z-index: 9999;
      }
      .loading-text {
        margin-top: 2rem;
        font-size: 0.9rem;
        color: #64748b;
        letter-spacing: 0.03125rem;
      }
      /* HTML: <div class="loader"></div> */
      .loader {
        height: 1rem;
        aspect-ratio: 4;
        --_g: no-repeat radial-gradient(farthest-side, red 90%, #0000);
        background: var(--_g) left, var(--_g) right;
        background-size: 25% 100%;
        display: grid;
      }
      .loader:before,
      .loader:after {
        content: "";
        height: inherit;
        aspect-ratio: 1;
        grid-area: 1/1;
        margin: auto;
        border-radius: 50%;
        transform-origin: -100% 50%;
        background: red;
        animation: l49 1s infinite linear;
      }
      .loader:after {
        transform-origin: 200% 50%;
        --s: -1;
        animation-delay: -0.5s;
      }

      @keyframes l49 {
        58%,
        100% {
          transform: rotate(calc(var(--s, 1) * 1turn));
        }
      }
    </style>

    <!-- GrowingIO Analytics code version 2.1 -->
    <!-- Copyright 2015-2018 GrowingIO, Inc. More info available at http://www.growingio.com -->

    <script type="text/javascript">
      if (
        location.hostname === "echolab.club" &&
        localStorage.getItem("skipAnalytics") !== "true" &&
        !location.pathname.includes("/admin") &&
        !location.pathname.includes("/content") &&
        !location.pathname.includes("/editor")
      ) {
        // window._gr_ignore_local_rule = true;
        !(function (e, t, n, g, i) {
          (e[i] =
            e[i] ||
            function () {
              (e[i].q = e[i].q || []).push(arguments);
            }),
            (n = t.createElement("script")),
            (tag = t.getElementsByTagName("script")[0]),
            (n.async = 1),
            (n.src =
              ("https:" == document.location.protocol
                ? "https://"
                : "http://") + g),
            tag.parentNode.insertBefore(n, tag);
        })(window, document, "script", "assets.giocdn.com/2.1/gio.js", "gio");
        gio("init", "9c11d8e6f8077ecd", {});
        gio("send");
      }
    </script>
  </head>
  <body>
    <script>
      // 只在线上环境且满足条件时加载ARMS监控
      if (
        location.hostname === "echolab.club" &&
        localStorage.getItem("skipAnalytics") !== "true" &&
        !location.pathname.includes("/admin") &&
        !location.pathname.includes("/content") &&
        !location.pathname.includes("/editor")
      ) {
        !(function (c, b, d, a) {
          c[a] || (c[a] = {});
          c[a] = {
            pid: "dq55fcf310@8a659a0085b712a",
            endpoint: "https://dq55fcf310-default-cn.rum.aliyuncs.com",
            remoteConfig: {
              region: "cn-hangzhou",
            },
          };
          with (b)
            with (body)
              with (insertBefore(createElement("script"), firstChild))
                setAttribute("crossorigin", "", (src = d));
        })(
          window,
          document,
          "https://dq55fcf310-sdk.rum.aliyuncs.com/v2/browser-sdk.js",
          "__rum"
        );
      }
    </script>
    <div id="app"></div>
    <div id="loading" class="loading-container">
      <div class="loader">
      </div>
      <div class="loading-text">别着急，正在努力加载中...</div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
