<!--
  公开内容卡片组件
  用于在首页展示公开的视频内容
-->
<template>
  <div class="public-content-card" :class="{ 'mobile-layout': isMobile }" @click="handleCardClick">
    <!-- 卡片缩略图 -->
    <div class="card-thumbnail">
      <ResponsiveImage v-if="content.thumbnailUrl" :src="content.thumbnailUrl" alt="缩略图" loading="lazy" />
      <div v-else class="thumbnail-placeholder">
        <el-icon :size="isMobile ? 24 : 32">
          <i-ep-video-play />
        </el-icon>
      </div>

      <!-- 操作按钮区域 -->
      <div class="card-actions" @click.stop>
        <slot name="actions"></slot>
      </div>

      <!-- 图片底部信息 -->
      <div class="thumbnail-overlay" v-if="(showAuthor && content.creator) || content.viewCount">
        <div class="overlay-left" v-if="showAuthor && content.creator">
          <div class="overlay-author" @click.stop="goToUserSpace(content.creator.id)">
            {{ getAuthorDisplayName(content.creator) }}
          </div>
        </div>
        <div class="overlay-right">
          <div v-if="content.viewCount" class="overlay-views">
            👁️ {{ content.viewCount }}
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="card-title">{{ content.name }}</h3>
      <div class="card-footer">
        <span class="card-date">{{ formatDate(content.createdAt) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useRouter } from 'vue-router';
import { isMobileDevice } from '@/utils/deviceDetector';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

const router = useRouter();

// 设备检测
const isMobile = ref(isMobileDevice());

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  showAuthor: {
    type: Boolean,
    default: true
  }
});

// 格式化日期为相对时间
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);

  // 根据时间差返回不同的格式
  if (diffSec < 60) {
    return '刚刚';
  } else if (diffMin < 60) {
    return `${diffMin}分钟前`;
  } else if (diffHour < 24) {
    return `${diffHour}小时前`;
  } else if (diffDay < 30) {
    return `${diffDay}天前`;
  } else if (diffMonth < 12) {
    return `${diffMonth}个月前`;
  } else {
    return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'numeric', day: 'numeric' });
  }
};

// 解析标签
const parseTags = (tagsStr) => {
  if (!tagsStr) return [];
  if (Array.isArray(tagsStr)) return tagsStr;
  return tagsStr.split(',').filter(tag => tag.trim()); // 显示所有标签
};

// 处理卡片点击（新标签页）
const handleCardClick = () => {
  window.open(`/player/${props.content.id}`, '_blank');
};

// 跳转到用户空间
const goToUserSpace = (userId) => {
  router.push(`/user/${userId}`);
};

// 获取作者显示名称
const getAuthorDisplayName = (creator) => {
  if (!creator) return '未知作者';

  // 优先显示用户名
  if (creator.username && creator.username.trim()) {
    return creator.username;
  }

  // 显示用户ID
  if (creator.id) {
    return creator.id;
  }

  return '未知作者';
};


</script>

<style scoped>
.public-content-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.public-content-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

/* 移除悬停时显示播放按钮的效果 */
/* .public-content-card:hover .play-overlay {
  opacity: 1;
} */

.card-thumbnail {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  background-color: #f5f7fa;
  overflow: hidden;
}



.thumbnail-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

/* 操作按钮区域 */
.card-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 2;
  display: flex;
  gap: 0.25rem;
}

/* 图片底部覆盖层 */
.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 0.75rem;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 10;
  pointer-events: none;
}

.thumbnail-overlay > * {
  pointer-events: auto;
}

.overlay-left {
  margin-right: auto;
}

.overlay-right {
  flex-shrink: 0;
}

.overlay-author {
  color: #fff;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overlay-author:hover {
  text-decoration: underline;
}

.overlay-views {
  color: #fff;
  font-size: 0.875rem;
  white-space: nowrap;
}

.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  height: 2.8rem;
  min-height: 2.8rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.card-tags {
  display: flex;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.tag-item {
  max-width: 5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-date {
  font-size: 0.7rem;
  color: #909399;
  white-space: nowrap;
  margin-left: 0.5rem;
}

/* 播放按钮覆盖层样式已移除 */

/* 手机端样式优化 */
.mobile-layout.public-content-card {
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f2f5;
}

.mobile-layout.public-content-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
}

.mobile-layout .thumbnail-overlay {
  padding: 0.375rem 0.5rem;
}

.mobile-layout .overlay-author {
  font-size: 0.75rem;
}

.mobile-layout .overlay-views {
  font-size: 0.75rem;
}

.mobile-layout .card-content {
  padding: 0.5rem;
}

.mobile-layout .card-title {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  -webkit-line-clamp: 2;
  line-height: 1.3;
  height: 2.21rem;
  min-height: 2.21rem;
}

.mobile-layout .card-footer {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.25rem;
}

.mobile-layout .card-tags {
  flex: 1;
  overflow: hidden;
  display: flex;
  gap: 0.25rem;
}

.mobile-layout .tag-item {
  max-width: 4rem;
  font-size: 0.65rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  flex-shrink: 0;
}

.mobile-layout .card-date {
  font-size: 0.6rem;
  color: #909399;
  white-space: nowrap;
  flex-shrink: 0;
  margin-left: 0.375rem;
}

/* 统一的响应式设计，不使用媒体查询 */
</style>
