<!--
  节点编辑器组件
  整合节点画布和属性面板
-->
<template>
  <div class="node-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="success" @click="importProject">
            <el-icon class="el-icon--left">
              <i-ep-upload />
            </el-icon>导入
          </el-button>
          <el-button type="success" @click="exportProject">
            <el-icon class="el-icon--left">
              <i-ep-download />
            </el-icon>导出
          </el-button>
        </el-button-group>
        <!-- 隐藏的文件输入框，用于导入文件 -->
        <input type="file" ref="fileInput" style="display: none" accept=".json" @change="handleFileImport" />
      </div>

      <div class="toolbar-center">
        <h2 class="editor-title">{{ projectTitle }}</h2>
        <el-tag v-if="isDirty" type="warning" size="small" style="margin-left: 8px">
          有未保存的更改
        </el-tag>
      </div>

      <div class="toolbar-right">
        <el-button @click="openSpeakerMappingDialog">
          <el-icon class="el-icon--left">
            <i-ep-user />
          </el-icon>说话人映射
        </el-button>
        <el-button class="save-button" type="primary" @click="saveProject" :disabled="saving">
          <el-icon class="el-icon--left">
            <i-ep-document />
          </el-icon>{{ saving ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <div class="canvas-container">
        <NodeCanvas
          @node-selected="handleNodeSelected"
          @node-updated="handleNodeUpdated"
          ref="canvasRef" />
      </div>

      <div class="properties-container" v-if="showProperties">
        <NodeProperties :selectedNodeId="selectedNodeId" @node-updated="handleNodeUpdated" />
      </div>
    </div>

    <!-- 保存对话框 -->
    <StandardDialog v-model="saveDialogVisible" title="保存内容" width="60%" :show-confirm="true"
      :confirm-text="saving ? '保存中...' : '保存'" cancel-text="取消" :close-on-click-modal="false"
      :confirm-closes-dialog="false" :confirm-disabled="saving"
      @confirm="handleSave">
      <el-form :model="saveForm" label-width="5rem">
        <el-form-item label="标题" required>
          <el-input v-model="saveForm.title" placeholder="请输入标题（1-100个字符）" :maxlength="100" show-word-limit
            :disabled="saving"></el-input>
        </el-form-item>
        <el-form-item label="学习语言" required>
          <el-select v-model="saveForm.learningLanguage" placeholder="请选择内容的目标学习语言" :disabled="saving">
            <el-option v-for="language in supportedLanguages" :key="language.value" :label="language.label" :value="language.value">
              <span class="language-option">
                <span class="language-name">{{ language.label }}</span>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过滤标签">
          <ContentFilterSelector
            v-model="selectedFilters"
            :max-selection="20"
            :language="saveForm.learningLanguage"
            @change="handleFilterChange"
          />
          <div class="form-help-text">过滤器数量：{{ selectedFilters.length }}/20 | 用于内容分类和筛选</div>
        </el-form-item>
        <el-form-item label="关键词">
          <div class="keywords-section">
            <div class="keywords-input">
              <el-input
                v-model="keywordInput"
                placeholder="输入关键词后按回车添加"
                :disabled="saving"
                @keyup.enter="addKeyword"
                style="flex: 1; margin-right: 8px;"
              />
              <el-button @click="addKeyword" :disabled="saving">添加</el-button>
              <el-button 
                type="primary" 
                @click="extractKeywords" 
                :loading="extractingKeywords"
                :disabled="saving"
              >
                {{ extractingKeywords ? '提取中...' : '智能提取' }}
              </el-button>
            </div>
            <div class="keywords-list" v-if="saveForm.keywords.length > 0">
              <el-tag
                v-for="(keyword, index) in saveForm.keywords"
                :key="index"
                closable
                @close="removeKeyword(index)"
                style="margin: 4px 4px 0 0;"
              >
                {{ keyword }}
              </el-tag>
            </div>
            <div class="form-help-text">关键词数量：{{ saveForm.keywords.length }}/8 | 用于SEO优化和内容描述</div>
          </div>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="saveForm.description" type="textarea" :rows="4" placeholder="请输入描述（选填，最多500个字符）"
            :maxlength="500" show-word-limit :disabled="saving"></el-input>
        </el-form-item>
      </el-form>
    </StandardDialog>

    <!-- 说话人映射对话框 -->
    <StandardSpeakerMappingDialog v-model="speakerMappingDialogVisible" @saved="handleSpeakerMappingSaved" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import NodeCanvas from './NodeCanvas.vue';
import NodeProperties from './NodeProperties.vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import contentService from "@/services/contentService";
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '@/components/common/StandardDialog.vue';
import StandardSpeakerMappingDialog from '@/components/settings/StandardSpeakerMappingDialog.vue';

import { checkFeaturePermission } from '@/services/featurePermissionService';
import { useLanguageStore } from '@/stores/languageStore';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import httpClient from '@/utils/httpClient';
import { useUnsavedChanges } from '@/composables/useUnsavedChanges';
import ContentFilterSelector from '@/components/content/ContentFilterSelector.vue';
import { API_ENDPOINTS } from '@/config/api';

const props = defineProps({
  projectId: {
    type: [String, Number],
    default: null
  },
  title: {
    type: String,
    default: '节点编辑器'
  },
  initialData: {
    type: Object,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const nodeStore = useNodeStore();
const languageStore = useLanguageStore();
const canvasRef = ref(null);
const selectedNodeId = ref(null);
const showProperties = ref(true);
const fileInput = ref(null);

// 未保存更改检测
const {
  isDirty,
  markAsModified,
  markAsSaved,
  reset: resetUnsavedChanges,
  confirmLeave
} = useUnsavedChanges();

// 保存初始状态的快照，用于对比是否有变化
const initialSnapshot = ref(null);
const formSnapshot = ref(null);

// 支持的语言列表
const supportedLanguages = SUPPORTED_LANGUAGES;

// 保存相关
const saveDialogVisible = ref(false);
const saveForm = ref({
  title: '',
  description: '',
  learningLanguage: '',
  keywords: []
});

// 过滤器列表
const selectedFilters = ref([]);

// 可用过滤器选项
const availableFilters = ref({});

// 计算当前语言的可用过滤器选项
const availableFilterOptions = computed(() => {
  const currentLanguage = saveForm.value.learningLanguage;
  const allFilters = [];

  // 添加语言等级过滤器
  if (availableFilters.value.language_level) {
    const languageLevels = availableFilters.value.language_level.filter(filter =>
      filter.language === currentLanguage || filter.language === null
    );
    allFilters.push(...languageLevels);
  }

  // 添加其他类型的过滤器
  ['content_type', 'topic', 'material'].forEach(type => {
    if (availableFilters.value[type]) {
      const typeFilters = availableFilters.value[type].filter(filter =>
        filter.language === currentLanguage || filter.language === null
      );
      allFilters.push(...typeFilters);
    }
  });

  return allFilters.map(filter => ({
    value: filter.id,
    label: filter.name,
    type: filter.type
  }));
});

// 说话人映射对话框
const speakerMappingDialogVisible = ref(false);

// 关键词相关
const extractingKeywords = ref(false);
const keywordInput = ref('');

// 获取过滤器数据
async function loadFilters() {
  try {
    const response = await httpClient.get('/api/filters');
    if (response.success) {
      availableFilters.value = response.data;
    }
  } catch (error) {
    console.error('获取过滤器失败:', error);
  }
}

// 处理过滤器变化
function handleFilterChange(filterIds, filtersByType) {
  selectedFilters.value = filterIds;
  // 可以在这里处理按类型分组的过滤器数据
}

// 在组件挂载时初始化数据
onMounted(async () => {
  // 加载过滤器数据
  await loadFilters();

  if (props.initialData) {
    // 设置表单数据
    let keywords = [];
    if (props.initialData.keywords) {
      try {
        keywords = typeof props.initialData.keywords === 'string' 
          ? JSON.parse(props.initialData.keywords) 
          : props.initialData.keywords;
      } catch (e) {
        keywords = [];
      }
    }
    
    saveForm.value = {
      title: props.initialData.name || '',
      description: props.initialData.description || '',
      learningLanguage: props.initialData.learningLanguage || '',
      keywords: keywords
    };

    // 加载现有的过滤器
    if (props.projectId) {
      try {
        const contentResponse = await httpClient.get(`${API_ENDPOINTS.CONTENTS.BASE}/${props.projectId}`);
        if (contentResponse.success && contentResponse.content.filtersGrouped) {
          // 提取所有过滤器ID
          const filterIds = [];
          Object.values(contentResponse.content.filtersGrouped).forEach(typeFilters => {
            typeFilters.forEach(filter => {
              filterIds.push(filter.id);
            });
          });
          selectedFilters.value = filterIds;
        }
      } catch (error) {
        console.error('加载内容过滤器失败:', error);
      }
    }

    // 初始化过滤器
    if (props.initialData.filterIds && Array.isArray(props.initialData.filterIds)) {
      selectedFilters.value = [...props.initialData.filterIds];
    }

    // 如果有配置数据，导入到编辑器
    if (props.initialData.configJson) {
      const config = typeof props.initialData.configJson === 'string'
        ? JSON.parse(props.initialData.configJson)
        : props.initialData.configJson;
      nodeStore.importNodes(config);
    }

    // 初始化完成后重置未保存状态
    resetUnsavedChanges();
  }

  // 保存初始状态快照
  nextTick(() => {
    const snapshot = createSnapshot();
    initialSnapshot.value = snapshot.nodes;
    formSnapshot.value = snapshot.form;
  });
});

// 生成当前状态快照
function createSnapshot() {
  return {
    nodes: JSON.stringify(nodeStore.exportNodes()),
    form: JSON.stringify({
      title: saveForm.value.title,
      description: saveForm.value.description,
      learningLanguage: saveForm.value.learningLanguage,
      keywords: saveForm.value.keywords,
      filters: selectedFilters.value
    })
  };
}

// 检查是否有未保存的更改
function checkForChanges() {
  if (!initialSnapshot.value || !formSnapshot.value) {
    return false;
  }

  const currentSnapshot = createSnapshot();
  const hasNodeChanges = currentSnapshot.nodes !== initialSnapshot.value;
  const hasFormChanges = currentSnapshot.form !== formSnapshot.value;

  const isDirtyNow = hasNodeChanges || hasFormChanges;

  if (isDirtyNow !== isDirty.value) {
    if (isDirtyNow) {
      markAsModified();
    } else {
      markAsSaved();
    }
  }
}

// 监听节点变化
watch(() => nodeStore.getAllNodes, () => {
  checkForChanges();
}, { deep: true });

// 监听表单变化
watch([saveForm, selectedFilters], () => {
  checkForChanges();
}, { deep: true });

// 智能提取关键词
async function extractKeywords() {
  if (extractingKeywords.value) return;
  
  extractingKeywords.value = true;
  try {
    const data = nodeStore.exportNodes();
    if (!data || !data.nodes) {
      ElMessage.warning('请先添加内容节点后再提取关键词');
      return;
    }

    // 提取文本内容
    const texts = [];
    Object.values(data.nodes).forEach(node => {
      if (node.type === 'textContent' && node.params?.text) {
        texts.push(node.params.text);
      }
    });
    
    const text = texts.join('\n').trim();
    if (!text) {
      ElMessage.warning('未找到文本内容');
      return;
    }

    const response = await httpClient.post('/api/keywords/extract', { text });

    if (response.success && response.keywords) {
      saveForm.value.keywords = response.keywords;
      ElMessage.success(`成功提取到 ${response.keywords.length} 个关键词`);
    } else {
      ElMessage.warning('未能提取到关键词');
    }
  } catch (error) {
    console.error('提取关键词失败:', error);
    ElMessage.error('提取关键词失败: ' + (error.message || '网络错误'));
  } finally {
    extractingKeywords.value = false;
  }
}

// 添加关键词
function addKeyword() {
  const keyword = keywordInput.value.trim();
  if (!keyword) return;
  
  if (saveForm.value.keywords.includes(keyword)) {
    ElMessage.warning('关键词已存在');
    return;
  }
  
  if (saveForm.value.keywords.length >= 8) {
    ElMessage.warning('关键词数量不能超过8个');
    return;
  }
  
  saveForm.value.keywords.push(keyword);
  keywordInput.value = '';
}

// 删除关键词
function removeKeyword(index) {
  saveForm.value.keywords.splice(index, 1);
}

// 组件销毁时清理数据
onUnmounted(() => {
  // 清空节点数据
  nodeStore.clearAllNodes();
});

// 项目标题
const projectTitle = computed(() => {
  if (props.isEditing) {
    return `编辑内容: ${saveForm.value.title || props.title}`;
  }
  return '新建内容';
});

// 处理节点选中
function handleNodeSelected(nodeId) {
  selectedNodeId.value = nodeId;
}



// 处理节点更新
function handleNodeUpdated(nodeId) {
  // ResourceNode 已经处理完成并缓存了结果，这里不需要额外处理
  // 节点更新通常是自动处理，不标记为用户修改
}

// 导出项目为JSON文件
async function exportProject() {
  try {
    // 导出节点数据
    const data = nodeStore.exportNodes();

    // 导入加密工具
    const { encryptJSON } = await import('@/utils/cryptoService');

    // 创建导出对象
    const exportData = {
      title: saveForm.value.title || '未命名项目',
      description: saveForm.value.description || '',
      filters: selectedFilters.value,
      configJson: encryptJSON(data) // 加密配置数据
    };



    // 创建JSON字符串（压缩格式）
    const jsonString = JSON.stringify(exportData);

    // 创建Blob对象
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);

    // 创建临时下载链接
    const link = document.createElement('a');
    link.href = url;

    // 设置文件名 - 使用当前日期时间作为文件名
    const date = new Date();
    const fileName = `echo-lab-project-${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}-${date.getHours().toString().padStart(2, '0')}${date.getMinutes().toString().padStart(2, '0')}.json`;
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success('项目已导出');
  } catch (error) {
    console.error('导出项目失败:', error);
    ElMessage.error('导出项目失败: ' + error.message);
  }
}

// 触发文件选择对话框
function importProject() {
  // 触发文件输入框的点击事件
  fileInput.value.click();
}

// 处理文件导入
function handleFileImport(event) {
  const file = event.target.files[0];
  if (!file) {
    return;
  }

  // 确认是否导入
  ElMessageBox.confirm('导入项目将覆盖当前画布，是否继续？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 创建文件读取器
    const reader = new FileReader();

    // 设置读取完成的回调
    reader.onload = async (e) => {
      try {
        // 解析JSON数据
        const importData = JSON.parse(e.target.result);

        // 导入解密工具
        const { decryptJSON } = await import('@/utils/cryptoService');

        let nodeData;

        // 检查是否是导出的完整项目文件（包含标题、描述等）
        if (importData.configJson) {
          // 尝试解密configJson
          nodeData = decryptJSON(importData.configJson);

          // 更新表单数据
          if (importData.title) saveForm.value.title = importData.title;
          if (importData.description) saveForm.value.description = importData.description;

          // 导入过滤器数据
          if (importData.filters && Array.isArray(importData.filters)) {
            selectedFilters.value = [...importData.filters];
          }
        } else {
          // 直接使用导入的数据作为节点数据
          nodeData = importData;
        }

        // 导入节点数据
        nodeStore.importNodes(nodeData);

        // 监听器会自动检测到变化



        ElMessage.success('项目已导入');
      } catch (error) {
        console.error('导入项目失败:', error);
        ElMessage.error('导入项目失败: ' + (error.message || '文件格式不正确'));
      }
    };

    // 设置读取错误的回调
    reader.onerror = () => {
      ElMessage.error('读取文件失败');
    };

    // 读取文件
    reader.readAsText(file);
  }).catch(() => {
    // 取消操作
  }).finally(() => {
    // 重置文件输入框，以便于下次选择同一个文件时也能触发change事件
    event.target.value = '';
  });
}



// 保存状态
const saving = ref(false);

// 数据验证函数
function validateSaveData() {
  // 1. 验证标题
  const title = saveForm.value.title?.trim();
  if (!title) {
    return { valid: false, message: '请输入标题' };
  }
  if (title.length > 100) {
    return { valid: false, message: '标题长度不能超过100个字符' };
  }

  // 2. 验证描述
  const description = saveForm.value.description?.trim() || '';
  if (description.length > 500) {
    return { valid: false, message: '描述长度不能超过500个字符' };
  }

  // 3. 验证节点数据
  const data = nodeStore.exportNodes();
  if (!data || !data.nodes || Object.keys(data.nodes).length === 0) {
    return { valid: false, message: '请至少添加一个节点后再保存' };
  }

  // 4. 验证过滤器
  if (selectedFilters.value.length > 20) {
    return { valid: false, message: '过滤器数量不能超过20个' };
  }

  // 5. 验证必要节点类型
  const nodeTypes = Object.values(data.nodes).map(node => node.type);
  const hasTextContent = nodeTypes.includes('textContent');
  const hasTextSequence = nodeTypes.includes('textSequence');

  if (!hasTextContent && !hasTextSequence) {
    return { valid: false, message: '项目必须包含文本内容节点或文本序列节点' };
  }

  return { valid: true, data, title, description };
}

// 保存项目
async function saveProject() {
  // 直接打开保存对话框，因为初始数据已经在组件挂载时设置好了
  saveDialogVisible.value = true;
}

// 处理保存
async function handleSave() {
  // 防止重复保存
  if (saving.value) {
    ElMessage.warning('正在保存中，请稍候...');
    return;
  }

  saving.value = true;

  try {
    // 1. 权限检查
    const hasPermission = await checkFeaturePermission('content_creation');
    if (!hasPermission) {
      ElMessage.warning('您没有内容创建权限，请联系管理员或升级会员等级');
      return;
    }

    // 2. 数据验证
    const validation = validateSaveData();
    if (!validation.valid) {
      ElMessage.warning(validation.message);
      return;
    }

    const { data, title, description } = validation;

    // 3. 处理加密
    let configJson;
    try {
      const { encryptJSON } = await import('@/utils/cryptoService');
      configJson = encryptJSON(data);
    } catch (encryptError) {
      console.warn('加密失败，使用原始数据:', encryptError);
      configJson = data; // 降级到不加密
      ElMessage.warning('数据加密失败，将以明文保存');
    }

    // 4. 查找视频配置节点，获取封面图链接
    let coverImageUrl = '';
    let coverDuration = 1.5;

    if (data && data.nodes) {
      const videoConfigNode = Object.values(data.nodes).find(node => node.type === 'videoConfig');
      if (videoConfigNode && videoConfigNode.params && videoConfigNode.params.cover) {
        coverImageUrl = videoConfigNode.params.cover.imageUrl || '';
        coverDuration = videoConfigNode.params.cover.duration || 1.5;
      }
    }

    // 5. 准备保存数据
    const content = {
      name: title,
      description: description,
      configJson: configJson,
      thumbnailUrl: coverImageUrl,
      learningLanguage: saveForm.value.learningLanguage || languageStore.currentLearningLanguage || 'ja',
      filterIds: selectedFilters.value,
      keywords: saveForm.value.keywords
    };

    // 6. 调用保存接口
    let response;
    let isNewContent = !props.isEditing;

    try {
      if (props.isEditing && props.projectId) {
        // 更新现有内容
        response = await contentService.updateContent(props.projectId, content);
      } else {
        // 创建新内容
        response = await contentService.createContent(content);
      }
    } catch (apiError) {
      // 处理API错误 - httpClient已经统一处理了错误格式
      let errorMessage = '保存失败';

      // httpClient抛出的错误已经是处理过的格式
      if (apiError.message) {
        errorMessage = apiError.message;
      } else if (typeof apiError === 'string') {
        errorMessage = apiError;
      }

      throw new Error(errorMessage);
    }

    // 7. 检查响应
    if (!response || !response.success) {
      throw new Error(response?.error || "保存失败");
    }

    const savedContent = response.content;



    ElMessage.success('保存成功');

    // 标记内容已保存并更新快照
    markAsSaved();
    const snapshot = createSnapshot();
    initialSnapshot.value = snapshot.nodes;
    formSnapshot.value = snapshot.form;

    // 9. 如果是新建内容，重定向到编辑模式
    if (isNewContent) {
      try {
        window.location.href = `/editor/${savedContent.id}`;
      } catch (redirectError) {
        console.error('页面跳转失败:', redirectError);
        ElMessage.warning('保存成功，但页面跳转失败，请手动刷新页面');
      }
    }

    // 保存成功后关闭对话框
    saveDialogVisible.value = false;
    return savedContent;
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(error.message || '保存失败，请稍后重试');
    // 保存失败时不关闭对话框，让用户可以重试
  } finally {
    saving.value = false;
  }
}

// 打开说话人映射对话框
function openSpeakerMappingDialog() {
  speakerMappingDialogVisible.value = true;
}

// 处理说话人映射保存
function handleSpeakerMappingSaved(mappings) {
  ElMessage.success('说话人映射已保存');
}

// 暴露给父组件的方法
defineExpose({
  isDirty,
  confirmLeave,
  markAsSaved,
  markAsModified,
  resetUnsavedChanges
});
</script>

<style scoped>
.node-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  border-bottom: 0.0625rem solid #e4e7ed;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.toolbar-center {
  flex: 2;
  text-align: center;
}

.toolbar-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.editor-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #303133;
}

.save-button {
  min-width: 5.625rem;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  overflow: hidden;
}

.properties-container {
  width: 25%;
  overflow-y: auto;
  background-color: #ffffff;
  border-left: 0.0625rem solid #e4e7ed;
}

.form-help-text {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-flag {
  font-size: 1.2rem;
}

.language-name {
  font-size: 0.875rem;
}

.keywords-section {
  width: 100%;
}

.keywords-input {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.keywords-list {
  min-height: 32px;
  margin-bottom: 4px;
}
</style>
