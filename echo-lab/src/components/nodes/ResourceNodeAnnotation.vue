<!--
  资源节点标注组件
  处理文本标注功能
-->
<template>
  <div class="annotation-component">
    <div class="preview-section">
      <div class="preview-header">
        <div>标注：</div>
        <el-button-group>
          <el-button size="small" type="primary" @click="showDialog" :disabled="!hasSourceNode">
            <el-icon class="el-icon--left">
              <i-ep-edit />
            </el-icon>
            编辑标注
          </el-button>
        </el-button-group>
      </div>
      <div class="preview-info"
        :class="{ 'success-bg': getAnnotationPercentage() === 100 && totalAnnotationCount > 0, 'warning-bg': getAnnotationPercentage() < 100 && totalAnnotationCount > 0 }">
        <div class="preview-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: getAnnotationPercentage() + '%' }"></div>
          </div>
          <div class="progress-text">
            已标注: {{ annotationCount }}/{{ totalAnnotationCount }}
            <span v-if="getAnnotationPercentage() === 100" class="status-tag success">完成</span>
            <span v-else-if="totalAnnotationCount > 0" class="status-tag warning">未完成</span>
            <span v-else class="status-tag info">无需标注</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标注编辑对话框 -->
    <standard-dialog v-model="dialogVisible" title="编辑标注" width="80%">
      <div class="dialog-content">
        <div class="dialog-toolbar">
          <!-- 勾选控制区域 -->
          <div class="selection-controls">
            <el-checkbox
              :model-value="isAllSelected"
              @change="toggleSelectAll"
              :indeterminate="selectedCount > 0 && !isAllSelected"
            >
              全选
            </el-checkbox>
            <span class="selection-info">
              已选择: {{ selectedCount }}/{{ filteredAnnotations.length }}
            </span>
            <el-button
              size="small"
              type="warning"
              @click="selectUnannotated"
              :disabled="totalUnannotatedCount === 0"
            >
              选择未标注的 ({{ totalUnannotatedCount }})
            </el-button>
          </div>
        </div>

        <div class="annotations-table-container">
          <el-table :data="filteredAnnotations" border style="width: 100%" max-height="60vh">
            <el-table-column width="50" align="center" fixed="left">
              <template #default="{ row }">
                <el-checkbox
                  :model-value="selectedItems.has(row.id)"
                  @change="toggleItemSelection(row)"
                />
              </template>
            </el-table-column>

            <el-table-column label="序号" width="80" align="center" fixed="left">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>

            <el-table-column label="类型" width="100" fixed="left">
              <template #default="{ row }">
                <el-tag v-if="row.isKeyword || row.type === 'keyword'" size="small" type="warning">关键词</el-tag>
                <el-tag v-else-if="row.type === 'transition'" size="small" type="info">转场</el-tag>
                <el-tag v-else size="small" type="primary">普通文本</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="原文" min-width="200" fixed="left">
              <template #default="{ row }">
                <div class="original-text-cell">{{ row.content }}</div>
                <div class="language-detection">
                  <span class="language-label">语言:</span>
                  <el-tag size="small" type="info">{{ getLanguageLabel(row.detectedLanguage) }}</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="标注" min-width="300">
              <template #default="{ row }">
                <!-- 显示标注结果 -->
                <div v-if="row.characters && row.characters.length > 0" class="annotation-container">
                  <div class="character-annotation-preview">
                    <CharacterAnnotation :characters="row.characters"
                      @update:characters="updateCharacters(row, $event)" />
                  </div>
                </div>
                <el-input v-else v-model="row.annotation.reading" type="textarea" :rows="3"
                  :class="{ 'empty-annotation': !row.annotation || !row.annotation.reading || !row.annotation.reading.trim() }" />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <!-- 标注按钮始终在操作列 -->
                <el-button type="primary" size="small" @click="annotateItem(row)" :loading="row.isAnnotating">
                  <el-icon v-if="!row.isAnnotating" style="margin-right: 0.25rem;">
                    <i-ep-edit />
                  </el-icon>
                  标注
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAnnotations">保存修改</el-button>
          <el-dropdown>
            <el-button type="success" :loading="isAnnotating" :disabled="selectedCount === 0">
              <el-icon style="margin-right: 0.25rem;">
                <i-ep-edit />
              </el-icon>
              批量标注选中项 ({{ selectedCount }})
              <el-icon class="el-icon--right">
                <i-ep-arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="batchAnnotateSelected" :disabled="selectedCount === 0">
                  标注选中项 ({{ selectedCount }})
                </el-dropdown-item>
                <el-dropdown-item @click="batchAnnotateUnannotated" :disabled="selectedUnannotatedCount === 0">
                  补全选中项中的未标注 ({{ selectedUnannotatedCount }})
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import CharacterAnnotation from '../common/CharacterAnnotation.vue';
import { getLanguageLabel } from '@/config/languages';
import httpClient from '@/utils/httpClient';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  processedResult: {
    type: Object,
    default: () => null
  },
  params: {
    type: Object,
    required: true
  },
  hasSourceNode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:params', 'process-node']);

// 对话框状态
const dialogVisible = ref(false);
const isAnnotating = ref(false);

// 标注项
const annotationItems = ref([]);

// 勾选功能相关变量
const selectedItems = ref(new Set()); // 勾选的项目ID集合

// 标注相关计算属性
const annotationCount = computed(() => {
  if (!props.processedResult || !props.processedResult.sourceSegments || !props.processedResult.annotations) return 0;

  // 计算有内容的标注项数量
  const annotations = props.processedResult.annotations;
  let count = 0;

  // 遍历所有分句和关键词，检查是否有对应的标注
  props.processedResult.sourceSegments.forEach(segment => {
    // 检查分句标注
    if (annotations[segment.id]) {
      const annotation = annotations[segment.id];
      if (typeof annotation === 'object' && annotation !== null) {
        // 对象类型标注
        if (annotation.reading && annotation.reading.trim()) {
          count++;
        }
      } else if (annotation && typeof annotation === 'string' && annotation.trim()) {
        // 字符串类型标注
        count++;
      }
    }

    // 检查关键词标注
    if (segment.keywords && segment.keywords.length > 0) {
      segment.keywords.forEach(keyword => {
        if (annotations[keyword.id]) {
          const annotation = annotations[keyword.id];
          if (typeof annotation === 'object' && annotation !== null) {
            // 对象类型标注（日语）
            if (annotation.reading && annotation.reading.trim()) {
              count++;
            }
          } else if (annotation && typeof annotation === 'string' && annotation.trim()) {
            // 字符串类型标注（非日语）
            count++;
          }
        }
      });
    }
  });

  return count;
});

const totalAnnotationCount = computed(() => {
  if (!props.processedResult || !props.processedResult.sourceSegments) return 0;

  // 计算所有需要标注的项目数量（包括分句和关键词）
  let total = 0;
  props.processedResult.sourceSegments.forEach(segment => {
    // 计算分句本身
    total++;

    // 计算关键词
    if (segment.keywords && segment.keywords.length > 0) {
      total += segment.keywords.length;
    }
  });

  return total;
});

// 过滤标注
// 直接使用所有标注项，不再需要搜索过滤
const filteredAnnotations = computed(() => annotationItems.value);

// 勾选相关计算属性
const selectedCount = computed(() => selectedItems.value.size);

const isAllSelected = computed(() => {
  return filteredAnnotations.value.length > 0 &&
         selectedCount.value === filteredAnnotations.value.length;
});

const selectedUnannotatedCount = computed(() => {
  return filteredAnnotations.value.filter(item =>
    selectedItems.value.has(item.id) && !isAnnotated(item)
  ).length;
});

const totalUnannotatedCount = computed(() => {
  return filteredAnnotations.value.filter(item => !isAnnotated(item)).length;
});



// 获取标注完成百分比
function getAnnotationPercentage() {
  if (!props.processedResult || !props.processedResult.annotations || totalAnnotationCount.value === 0) {
    return 0;
  }
  return Math.round((annotationCount.value / totalAnnotationCount.value) * 100);
}

// 显示对话框
function showDialog() {
  // 强制重新处理节点
  emit('process-node');

  // 准备标注项
  prepareAnnotationItems();

  // 清理勾选状态
  selectedItems.value.clear();

  // 显示对话框
  dialogVisible.value = true;
}

// 准备标注项
function prepareAnnotationItems() {
  annotationItems.value = [];

  // 如果没有处理结果，但有参数中的标注数据，尝试从源节点获取分句
  if (!props.processedResult || !props.processedResult.sourceSegments) {
    // 如果有参数中的标注数据，直接使用
    if (props.params.annotations && Object.keys(props.params.annotations).length > 0) {
      Object.entries(props.params.annotations).forEach(([id, annotation]) => {
        // 尝试从音频项中获取内容
        let content = '';
        let detectedLanguage = 'ja'; // 默认为日语

        // 如果有音频项，尝试从中获取内容
        if (props.params.audioItems) {
          const audioItem = props.params.audioItems.find(item => item.id === id);
          if (audioItem) {
            content = audioItem.text || '';
            detectedLanguage = audioItem.language || 'ja';
          }
        }

        // 确保annotation是对象格式
        const annotationObj = typeof annotation === 'object' ? annotation : { reading: annotation || '', characters: [] };

        // 创建标注项
        annotationItems.value.push({
          id: id,
          content: content || '未找到原文',
          annotation: annotationObj,
          characters: annotationObj.characters || [],
          detectedLanguage: detectedLanguage,
          isAnnotating: false
        });
      });
      return;
    }
  }

  // 正常处理流程：从处理结果中获取数据
  if (props.processedResult && props.processedResult.sourceSegments && props.processedResult.sourceSegments.length > 0) {
    // 处理所有分句和它们的关键词
    props.processedResult.sourceSegments.forEach(segment => {
      // 使用源节点提供的语言信息
      const detectedLanguage = segment.language || 'auto';

      // 获取标注数据，优先使用处理结果中的标注
      const annotation = (props.processedResult.annotations && props.processedResult.annotations[segment.id]) ||
        (props.params.annotations && props.params.annotations[segment.id]) || '';

      // 确保annotation是对象格式
      const annotationObj = typeof annotation === 'object' ? annotation : { reading: annotation || '', characters: [] };

      // 创建标注项
      annotationItems.value.push({
        id: segment.id,
        content: segment.content,
        annotation: annotationObj,
        characters: annotationObj.characters || [],
        detectedLanguage: detectedLanguage,
        isAnnotating: false,
        type: segment.type || 'normal',
        isKeyword: segment.isKeyword || segment.type === 'keyword'
      });

      // 处理关键词
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach(keyword => {
          // 获取关键词的标注数据
          const keywordAnnotation = (props.processedResult.annotations && props.processedResult.annotations[keyword.id]) ||
            (props.params.annotations && props.params.annotations[keyword.id]) || '';

          // 关键词的语言通常与其所属分句相同
          const keywordLanguage = keyword.language || segment.language || 'auto';

          // 处理日语关键词标注
          if (keywordLanguage === 'ja') {
            // 确保annotation是对象格式
            const keywordAnnotationObj = typeof keywordAnnotation === 'object' ?
              keywordAnnotation : { reading: keywordAnnotation || '', characters: [] };

            // 创建关键词标注项
            annotationItems.value.push({
              id: keyword.id,
              content: keyword.text,
              annotation: keywordAnnotationObj,
              characters: keywordAnnotationObj.characters || [],
              detectedLanguage: keywordLanguage,
              isAnnotating: false,
              type: 'keyword',
              isKeyword: true,
              sourceSegmentId: segment.id
            });
          } else {
            // 非日语关键词标注
            annotationItems.value.push({
              id: keyword.id,
              content: keyword.text,
              annotation: keywordAnnotation,
              detectedLanguage: keywordLanguage,
              isAnnotating: false,
              type: 'keyword',
              isKeyword: true,
              sourceSegmentId: segment.id
            });
          }
        });
      }
    });
  }
}





// 批量标注选中的未标注项目
function batchAnnotateUnannotated() {
  const selectedUnannotatedItems = filteredAnnotations.value.filter(item =>
    selectedItems.value.has(item.id) && !isAnnotated(item)
  );

  if (selectedUnannotatedItems.length === 0) {
    ElMessage.warning('选中项目中没有需要标注的内容');
    return;
  }

  // 调用批量标注，但只处理选中的未标注项目
  return batchAnnotateSelectedItems(selectedUnannotatedItems, '选中的未标注');
}

// 标注单个项
async function annotateItem(item) {
  if (!item || !item.content) {
    return;
  }

  try {
    // 设置标注状态
    item.isAnnotating = true;

    // 调用批量标注API（单个项也使用批量接口），使用httpClient确保有超时设置
    const result = await httpClient.post('/api/annotate', {
      segments: [{
        id: item.id,
        content: item.content,
        language: item.detectedLanguage,
        type: item.type || 'normal',
        isKeyword: item.isKeyword || item.type === 'keyword'
      }]
    }, {
      timeout: 180000 // 3分钟超时，比后端的2分钟稍长
    });

    if (result && result.annotations && result.annotations[item.id]) {
      // 更新标注数据
      item.annotation = result.annotations[item.id];

      // 如果返回了字符级标注，更新字符数组
      if (item.annotation.characters && item.annotation.characters.length > 0) {
        item.characters = item.annotation.characters;
      }
    }
  } catch (error) {
    console.error('标注失败:', error);
    ElMessage.error('标注失败，请重试');
  } finally {
    item.isAnnotating = false;
  }
}

// 更新字符级标注
function updateCharacters(item, characters) {
  if (!item) return;

  // 更新字符数组
  item.characters = characters;

  // 更新标注的reading字段，确保与字符级标注一致
  if (item.annotation && typeof item.annotation === 'object') {
    // 从字符级标注重新生成reading
    const reading = characters.map(char => char.reading || char.char).join('');
    item.annotation.reading = reading;
  }
}

// 保存标注
function saveAnnotations() {
  if (annotationItems.value.length === 0) {
    dialogVisible.value = false;
    return;
  }

  // 更新标注
  const annotations = {};

  annotationItems.value.forEach(item => {
    // 确保annotation是一个对象
    if (typeof item.annotation !== 'object' || item.annotation === null) {
      item.annotation = {
        reading: item.annotation || '',
        characters: item.characters || []
      };
    }

    // 确保characters存在，并且与item.characters保持同步
    if (item.characters && item.characters.length > 0) {
      // 始终使用最新的字符级标注
      item.annotation.characters = [...item.characters];

      // 从字符级标注重新生成reading
      const reading = item.characters.map(char => char.reading || char.char).join('');
      item.annotation.reading = reading;
    }

    annotations[item.id] = item.annotation;
  });

  // 更新节点参数
  const updatedParams = { ...props.params, annotations };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 关闭对话框
  dialogVisible.value = false;

  ElMessage.success('标注已保存');
}

// 勾选相关方法
// 切换单个项目的勾选状态
function toggleItemSelection(item) {
  if (selectedItems.value.has(item.id)) {
    selectedItems.value.delete(item.id);
  } else {
    selectedItems.value.add(item.id);
  }
}

// 切换全选状态
function toggleSelectAll() {
  if (isAllSelected.value) {
    // 取消全选
    selectedItems.value.clear();
  } else {
    // 全选
    filteredAnnotations.value.forEach(item => {
      selectedItems.value.add(item.id);
    });
  }
}

// 选择未标注的项目
function selectUnannotated() {
  selectedItems.value.clear();
  filteredAnnotations.value.forEach(item => {
    if (!isAnnotated(item)) {
      selectedItems.value.add(item.id);
    }
  });
}

// 判断项目是否已标注
function isAnnotated(item) {
  if (!item.annotation) return false;

  if (typeof item.annotation === 'object') {
    return item.annotation.reading && item.annotation.reading.trim();
  } else {
    return typeof item.annotation === 'string' && item.annotation.trim();
  }
}

// 批量标注选中的项目
async function batchAnnotateSelected() {
  const selectedItemsList = filteredAnnotations.value.filter(item =>
    selectedItems.value.has(item.id)
  );

  if (selectedItemsList.length === 0) {
    ElMessage.warning('没有选中的项目');
    return;
  }

  return batchAnnotateSelectedItems(selectedItemsList, '选中');
}

// 批量标注选中的项目的核心逻辑
async function batchAnnotateSelectedItems(itemsToAnnotate, description) {
  if (itemsToAnnotate.length === 0) {
    return;
  }

  try {
    isAnnotating.value = true;

    // 显示加载提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: `正在标注${description}的 ${itemsToAnnotate.length} 个项目...`,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 准备批量标注请求
    const segments = itemsToAnnotate.map(item => ({
      id: item.id,
      content: item.content,
      language: item.detectedLanguage,
      type: item.type || 'normal',
      isKeyword: item.isKeyword || item.type === 'keyword'
    }));

    // 分批处理，每批最多10个
    const BATCH_SIZE = 10;
    const batches = [];

    for (let i = 0; i < segments.length; i += BATCH_SIZE) {
      batches.push(segments.slice(i, i + BATCH_SIZE));
    }

    console.log(`标注任务分为 ${batches.length} 批，每批最多 ${BATCH_SIZE} 个项目`);

    let successCount = 0;
    let failureCount = 0;

    // 处理每一批
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      // 更新加载提示
      loadingInstance.setText(`正在标注${description}的项目... (${batchIndex + 1}/${batches.length}批)`);

      try {
        // 调用批量标注API，使用httpClient确保有超时设置
        const result = await httpClient.post('/api/annotate', {
          segments: batch
        }, {
          timeout: 180000 // 3分钟超时，比后端的2分钟稍长
        });

        if (result && result.annotations) {
          successCount += Object.keys(result.annotations).length;

          // 更新标注结果
          Object.keys(result.annotations).forEach(id => {
            const items = annotationItems.value.filter(i => i.id === id);
            items.forEach(item => {
              // 更新标注数据
              item.annotation = result.annotations[id];

              // 如果返回了字符级标注，更新字符数组
              if (item.annotation.characters && item.annotation.characters.length > 0) {
                item.characters = item.annotation.characters;
              }
            });
          });
        }
      } catch (error) {
        console.error(`第 ${batchIndex + 1} 批标注失败:`, error);
        failureCount += batch.length;

        // 为失败的项目设置空标注，避免界面异常
        batch.forEach(segment => {
          const items = annotationItems.value.filter(i => i.id === segment.id);
          items.forEach(item => {
            if (!item.annotation) {
              item.annotation = {
                reading: segment.content || "",
                characters: [],
              };
            }
          });
        });
      }
    }

    // 关闭加载提示
    loadingInstance.close();

    // 显示最终结果
    if (successCount > 0) {
      ElMessage.success(`已完成${description}的标注: 成功 ${successCount} 个${failureCount > 0 ? `, 失败 ${failureCount} 个` : ''}`);
    } else {
      ElMessage.error(`${description}的标注全部失败`);
    }
  } catch (error) {
    console.error('批量标注失败:', error);
    ElMessage.error('批量标注失败: ' + error.message);
  } finally {
    isAnnotating.value = false;
  }
}
</script>

<style scoped>
/* 预览区域样式 */
.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

/* 对话框样式 */
.dialog-content {
  max-height: 100%;
  overflow-y: auto;
}

.dialog-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.selection-info {
  font-size: 0.875rem;
  color: #606266;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.annotations-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.original-text-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 6.25rem;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0.5rem;
  border-radius: 0.25rem;
  line-height: 1.5;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.language-detection {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.language-label {
  margin-right: 0.5rem;
}

.empty-annotation {
  border-color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.05);
}

.annotation-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.character-annotation-preview {
  width: 100%;
  min-height: 2rem;
  line-height: 1.5;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}
</style>
