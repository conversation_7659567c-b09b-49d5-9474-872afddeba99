import { defineStore } from "pinia";
import collectionService from "@/services/collectionService";

export const useCollectionStore = defineStore("collection", {
  state: () => ({
    loading: false,
    error: null,
    collections: [],
    publicCollections: [],
    currentCollection: null,
    pagination: {
      total: 0,
      page: 1,
      pageSize: 20,
    },
    publicPagination: {
      total: 0,
      page: 1,
      pageSize: 20,
    },
    filters: {
      search: "",
      status: "all",
      sortBy: "updated_at",
      sortOrder: "desc",
    },
    publicFilters: {
      search: "",
      sortBy: "updated_at",
      sortOrder: "desc",
    },
  }),

  getters: {
    // 获取合集统计信息
    collectionStats: (state) => {
      const total = state.collections.length;
      const published = state.collections.filter(c => c.status === "published").length;
      const draft = state.collections.filter(c => c.status === "draft").length;
      const publicCount = state.collections.filter(c => c.isPublic).length;

      return {
        total,
        published,
        draft,
        public: publicCount,
        private: total - publicCount,
      };
    },
  },

  actions: {
    // 获取用户合集列表
    async fetchUserCollections(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          page: params.page || this.pagination.page,
          pageSize: params.pageSize || this.pagination.pageSize,
          search: params.search || this.filters.search,
          status: params.status !== "all" ? params.status : undefined,
          sortBy: params.sortBy || this.filters.sortBy,
          sortOrder: params.sortOrder || this.filters.sortOrder,
        };

        const response = await collectionService.getUserCollections(queryParams);
        if (response && response.success) {
          // 支持追加模式（加载更多）
          if (params.append) {
            this.collections = [...this.collections, ...(response.collections || [])];
          } else {
            this.collections = response.collections || [];
          }

          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "获取合集列表失败");
        }
      } catch (err) {
        console.error("获取用户合集列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    // 获取公开合集列表
    async fetchPublicCollections(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          page: params.page || this.publicPagination.page,
          pageSize: params.pageSize || this.publicPagination.pageSize,
          search: params.search || this.publicFilters.search,
          sortBy: params.sortBy || this.publicFilters.sortBy,
          sortOrder: params.sortOrder || this.publicFilters.sortOrder,
        };

        const response = await collectionService.getPublicCollections(queryParams);
        if (response && response.success) {
          // 支持追加模式（加载更多）
          if (params.append) {
            this.publicCollections = [...this.publicCollections, ...(response.collections || [])];
          } else {
            this.publicCollections = response.collections || [];
          }

          if (response.pagination) {
            this.publicPagination = {
              ...this.publicPagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "获取公开合集列表失败");
        }
      } catch (err) {
        console.error("获取公开合集列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    // 获取合集详情
    async fetchCollectionById(id) {
      this.loading = true;
      try {
        const response = await collectionService.getCollectionById(id);
        if (response && response.success) {
          this.currentCollection = response.collection;
        } else {
          throw new Error(response?.error || "获取合集详情失败");
        }
      } catch (err) {
        console.error("获取合集详情失败:", err);
        this.error = err.message;
        throw err;
      } finally {
        this.loading = false;
      }
    },

    // 创建合集
    async createCollection(collectionData) {
      try {
        const response = await collectionService.createCollection(collectionData);
        if (response && response.success) {
          this.collections.unshift(response.collection);
          return response.collection;
        } else {
          throw new Error(response?.error || "创建合集失败");
        }
      } catch (err) {
        console.error("创建合集失败:", err);
        throw err;
      }
    },

    // 更新合集
    async updateCollection(id, updateData) {
      try {
        const response = await collectionService.updateCollection(id, updateData);
        if (response && response.success) {
          const index = this.collections.findIndex((c) => c.id === id);
          if (index !== -1) {
            this.collections[index] = { ...this.collections[index], ...response.collection };
          }
          if (this.currentCollection && this.currentCollection.id === id) {
            this.currentCollection = { ...this.currentCollection, ...response.collection };
          }
          return response.collection;
        } else {
          throw new Error(response?.error || "更新合集失败");
        }
      } catch (err) {
        console.error("更新合集失败:", err);
        throw err;
      }
    },

    // 删除合集
    async deleteCollection(id) {
      try {
        const response = await collectionService.deleteCollection(id);
        if (response && response.success) {
          this.collections = this.collections.filter((c) => c.id !== id);
          if (this.currentCollection && this.currentCollection.id === id) {
            this.currentCollection = null;
          }
        } else {
          throw new Error(response?.error || "删除合集失败");
        }
      } catch (err) {
        console.error("删除合集失败:", err);
        throw err;
      }
    },

    // 发布合集
    async publishCollection(id) {
      try {
        const response = await collectionService.publishCollection(id);
        if (response && response.success) {
          // 更新本地状态
          const index = this.collections.findIndex((c) => c.id === id);
          if (index !== -1) {
            this.collections[index] = { ...this.collections[index], ...response.collection };
          }
          if (this.currentCollection && this.currentCollection.id === id) {
            this.currentCollection = { ...this.currentCollection, ...response.collection };
          }
          return response.collection;
        } else {
          throw new Error(response?.error || "发布合集失败");
        }
      } catch (err) {
        console.error("发布合集失败:", err);
        throw err;
      }
    },

    // 下架合集
    async unpublishCollection(id) {
      try {
        const response = await collectionService.unpublishCollection(id);
        if (response && response.success) {
          // 更新本地状态
          const index = this.collections.findIndex((c) => c.id === id);
          if (index !== -1) {
            this.collections[index] = { ...this.collections[index], ...response.collection };
          }
          if (this.currentCollection && this.currentCollection.id === id) {
            this.currentCollection = { ...this.currentCollection, ...response.collection };
          }
          return response.collection;
        } else {
          throw new Error(response?.error || "下架合集失败");
        }
      } catch (err) {
        console.error("下架合集失败:", err);
        throw err;
      }
    },



    // 更新过滤条件
    updateFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters };
    },

    // 更新公开合集过滤条件
    updatePublicFilters(newFilters) {
      this.publicFilters = { ...this.publicFilters, ...newFilters };
    },

    // 更新分页信息
    updatePagination(newPagination) {
      this.pagination = { ...this.pagination, ...newPagination };
    },

    // 更新公开合集分页信息
    updatePublicPagination(newPagination) {
      this.publicPagination = { ...this.publicPagination, ...newPagination };
    },

    // 清除错误
    clearError() {
      this.error = null;
    },

    // 清除当前合集
    clearCurrentCollection() {
      this.currentCollection = null;
    },

    // 加载更多公开合集
    async loadMoreCollections() {
      if (this.loading || this.publicCollections.length >= this.publicPagination.total) {
        return;
      }

      this.publicPagination.page += 1;
      await this.fetchPublicCollections({
        append: true,
        page: this.publicPagination.page,
        pageSize: this.publicPagination.pageSize,
        sortBy: this.publicFilters.sortBy,
        sortOrder: this.publicFilters.sortOrder
      });
    },

    // 重置状态
    resetState() {
      this.collections = [];
      this.publicCollections = [];
      this.currentCollection = null;
      this.error = null;
      this.loading = false;
    },
  },
});
