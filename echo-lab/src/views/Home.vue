<!--
  首页视图
  使用Tab切换展示推荐内容、全部内容和热门合集
-->
<template>
  <div class="home-view" :class="{ 'mobile-layout': isMobile }">
    <!-- 左侧侧边栏 -->
    <div class="home-sidebar" :class="{ 'visible': sidebarVisible }">
      <HomeSidebar @close-sidebar="closeSidebar" />
    </div>

    <!-- 主内容区域 -->
    <div class="home-content">
      <!-- 首页标题栏 -->
      <div class="home-header">
        <!-- 左侧侧边栏按钮 -->
        <el-button @click="toggleSidebar" class="sidebar-toggle-btn" :type="isMobile ? 'text' : 'default'">
          <el-icon :size="isMobile ? 18 : 20">
            <i-ep-menu />
          </el-icon>
        </el-button>

        <!-- 中间标题 -->
        <h1 class="page-title">Echo Lab</h1>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <CheckInButton />
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="home-main">
        <!-- Tab切换 -->
        <div class="home-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="content-tabs">
            <el-tab-pane label="全部内容" name="all">
              <!-- 搜索栏和筛选按钮 -->
              <div class="search-filter-bar">
                <div class="search-section">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索内容..."
                    clearable
                    @keyup.enter="handleSearch"
                    @clear="handleSearch"
                    class="search-input"
                  >
                    <template #prefix>
                      <el-icon><i-ep-search /></el-icon>
                    </template>
                    <template #append>
                      <el-button type="primary" @click="handleSearch">搜索</el-button>
                    </template>
                  </el-input>
                </div>

                <div class="filter-section">
                  <FilterDialog
                    v-model="filters"
                    @search="handleFilterSearch"
                  />
                </div>
              </div>

              <AllContent
                :contents="contentList"
                :loading="contentLoading"
                :has-more="hasMore"
                :total="contentTotal"
                :filters="filters"
                :hide-header="true"
                :hide-filter="false"
                @update-filters="handleFiltersUpdate"
                @clear-filters="handleFiltersClear"
                @load-more="loadMoreContent"
              />
            </el-tab-pane>
            <el-tab-pane label="热门合集" name="collections">
              <div class="collections-content">
                <CollectionGrid
                  :collections="publicCollections"
                  :loading="collectionsLoading"
                  :show-actions="false"
                  :show-author="true"
                  :show-view-count="true"
                  :show-favorite-count="true"
                  :show-favorite-button="true"
                  :show-status="false"
                  :show-pagination="false"
                  :show-load-more="true"
                  :has-more="hasMoreCollections"
                  :total="collectionTotal"
                  empty-text="暂无热门合集"
                  @toggle-favorite="handleToggleFavorite"
                  @load-more="loadMoreCollections"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 遮罩层 - 点击关闭侧边栏 -->
    <div class="sidebar-overlay" v-if="sidebarVisible" @click="toggleSidebar"></div>





  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { usePublicContentStore } from '@/core/stores/publicContentStore';
import { useUserStore } from '@/stores/userStore';
import { useLanguageStore } from '@/stores/languageStore';
import { useCollectionStore } from '@/stores/collectionStore';
import { useCollectionFavoriteStore } from '@/stores/collectionFavoriteStore';
import HomeSidebar from '@/components/common/HomeSidebar.vue';
import AllContent from '@/components/content/AllContent.vue';
import CollectionGrid from '@/components/collection/CollectionGrid.vue';
import CheckInButton from '@/components/common/CheckInButton.vue';
import FilterDialog from '@/components/content/FilterDialog.vue';


import { isMobileDevice } from '@/utils/deviceDetector';
import { ElMessage } from 'element-plus';



const router = useRouter();
const route = useRoute();
const publicContentStore = usePublicContentStore();
const userStore = useUserStore();
const languageStore = useLanguageStore();
const collectionStore = useCollectionStore();
const collectionFavoriteStore = useCollectionFavoriteStore();

// 侧边栏状态
const sidebarVisible = ref(false);

// Tab状态
const activeTab = ref('all');



// 当前学习语言
const currentLearningLanguage = computed(() => languageStore.currentLearningLanguage);

// 内容列表状态（统一的内容列表，不再区分推荐和全部）
const contentList = ref([]);
const contentLoading = ref(false);
const hasMore = ref(true);
const contentTotal = ref(0);
const searchQuery = ref('');

// 本地存储键名（按语言区分）
const getFiltersStorageKey = () => {
  const language = languageStore.currentLearningLanguage || 'ja';
  return `home_page_filters_${language}`;
};

// 保存筛选条件到本地存储
function saveFiltersToLocal(filtersData) {
  try {
    localStorage.setItem(getFiltersStorageKey(), JSON.stringify(filtersData));
  } catch (error) {
    console.warn('保存筛选条件失败:', error);
  }
}

// 从本地存储加载筛选条件
function loadFiltersFromLocal() {
  try {
    const saved = localStorage.getItem(getFiltersStorageKey());
    if (saved) {
      const parsed = JSON.parse(saved);
      // 验证数据结构
      if (parsed && typeof parsed === 'object') {
        return {
          search: parsed.search || '',
          filterIds: Array.isArray(parsed.filterIds) ? parsed.filterIds : [],
          filtersByType: parsed.filtersByType || {},
          languageLevels: Array.isArray(parsed.languageLevels) ? parsed.languageLevels : [],
          contentTypes: Array.isArray(parsed.contentTypes) ? parsed.contentTypes : [],
          topics: Array.isArray(parsed.topics) ? parsed.topics : [],
          materials: Array.isArray(parsed.materials) ? parsed.materials : []
        };
      }
    }
  } catch (error) {
    console.warn('加载筛选条件失败:', error);
  }

  // 返回默认值
  return {
    search: '',
    filterIds: [],
    filtersByType: {},
    languageLevels: [],
    contentTypes: [],
    topics: [],
    materials: []
  };
}

// 筛选条件
const filters = ref(loadFiltersFromLocal());

// 合集相关数据
const publicCollections = computed(() => collectionStore.publicCollections);
const collectionsLoading = computed(() => collectionStore.loading);
const collectionTotal = computed(() => collectionStore.publicPagination.total);
const hasMoreCollections = computed(() => {
  const pagination = collectionStore.publicPagination;
  return publicCollections.value.length < pagination.total;
});

// 设备检测
const isMobile = computed(() => isMobileDevice());

// 切换侧边栏显示状态
function toggleSidebar() {
  sidebarVisible.value = !sidebarVisible.value;
}

// 关闭侧边栏
function closeSidebar() {
  sidebarVisible.value = false;
}

// 搜索处理
function handleSearch() {
  filters.value.search = searchQuery.value;
  loadContent(true);
}

// 加载内容（统一的内容加载方法）
async function loadContent(refresh = false) {
  if (contentLoading.value && !refresh) return;

  try {
    contentLoading.value = true;

    // 构建筛选条件
    const filterParams = {
      search: filters.value.search,
      filterIds: filters.value.filterIds || [],
      filtersByType: filters.value.filtersByType || {}
    };

    // 语言筛选通过请求头 x-user-language 自动处理，不需要在这里设置

    await publicContentStore.fetchPublicContents(refresh, filterParams);

    if (refresh) {
      contentList.value = [...publicContentStore.contents];
    } else {
      // 加载更多时，需要合并内容
      const existingIds = new Set(contentList.value.map(item => item.id));
      const newContents = publicContentStore.contents.filter(item => !existingIds.has(item.id));
      contentList.value = [...contentList.value, ...newContents];
    }

    contentTotal.value = publicContentStore.pagination.total;
    hasMore.value = contentList.value.length < contentTotal.value;

  } catch (error) {
    console.error('加载内容失败:', error);
    ElMessage.error('加载内容失败，请重试');
  } finally {
    contentLoading.value = false;
  }
}

// 加载更多内容
async function loadMoreContent() {
  if (!hasMore.value || contentLoading.value) return;

  try {
    // 构建筛选条件
    const filterParams = {
      search: filters.value.search,
      filterIds: filters.value.filterIds || [],
      filtersByType: filters.value.filtersByType || {}
    };

    await publicContentStore.loadMoreContents(filterParams);

    // 更新本地状态
    contentList.value = [...publicContentStore.contents];
    contentTotal.value = publicContentStore.pagination.total;
    hasMore.value = contentList.value.length < contentTotal.value;

  } catch (error) {
    console.error('加载更多内容失败:', error);
    ElMessage.error('加载失败，请重试');
  }
}

// 处理过滤器更新
function handleFiltersUpdate(newFilters) {
  filters.value = { ...filters.value, ...newFilters };
  saveFiltersToLocal(filters.value);
  loadContent(true);
}

// 处理过滤器清除
function handleFiltersClear() {
  filters.value = {
    search: '',
    filterIds: [],
    filtersByType: {},
    languageLevels: [],
    contentTypes: [],
    topics: [],
    materials: []
  };
  searchQuery.value = '';
  loadContent(true);
}

// 处理筛选弹窗的搜索
function handleFilterSearch() {
  // 将新的过滤器格式转换为API需要的格式
  const filterIds = [
    ...filters.value.languageLevels,
    ...filters.value.contentTypes,
    ...filters.value.topics,
    ...filters.value.materials
  ];

  const filtersByType = {};
  if (filters.value.languageLevels.length > 0) {
    filtersByType.language_level = filters.value.languageLevels;
  }
  if (filters.value.contentTypes.length > 0) {
    filtersByType.content_type = filters.value.contentTypes;
  }
  if (filters.value.topics.length > 0) {
    filtersByType.topic = filters.value.topics;
  }
  if (filters.value.materials.length > 0) {
    filtersByType.material = filters.value.materials;
  }

  // 更新filters对象
  filters.value.filterIds = filterIds;
  filters.value.filtersByType = filtersByType;

  // 保存筛选条件到本地存储
  saveFiltersToLocal(filters.value);

  // 触发搜索
  loadContent(true);
}

// 处理过滤器筛选变化
function handleFilterChange(selectedFilterIds, filtersByType) {
  filters.value.filterIds = selectedFilterIds;
  filters.value.filtersByType = filtersByType; // 保存分类信息
  loadContent(true);
}

// 处理tab切换
function handleTabChange(tabName) {
  // 更新URL参数
  router.replace({
    path: route.path,
    query: { ...route.query, tab: tabName }
  });
}



// 初始化数据加载
async function initializeData() {
  // 确保语言store状态是最新的
  languageStore.loadLearningLanguage();

  // 加载内容列表
  await loadContent(true);

  // 加载公开合集
  await loadCollections();
}

// 监听语言变化
watch(currentLearningLanguage, async (newLanguage, oldLanguage) => {
  if (newLanguage !== oldLanguage) {
    // 语言变化时重置筛选条件并重新加载内容
    filters.value = loadFiltersFromLocal(); // 加载新语言的筛选条件
    searchQuery.value = filters.value.search || ''; // 同步搜索框
    await loadContent(true);
  }
});

// 监听用户等级变化
watch(() => languageStore.selectedLevels, async (newLevels, oldLevels) => {
  if (JSON.stringify(newLevels) !== JSON.stringify(oldLevels)) {
    // 等级变化时重新加载内容
    await loadContent(true);
  }
});

// 监听 tab 切换
watch(activeTab, async (newTab) => {
  if (newTab === 'collections' && publicCollections.value.length === 0) {
    // 切换到合集 tab 且还没有数据时，加载合集
    await loadCollections();
  }
});

// 监听路由变化，同步tab状态
watch(() => route.query.tab, (newTab) => {
  if (newTab === 'collections') {
    activeTab.value = 'collections';
  } else {
    activeTab.value = 'all';
  }
});

// 加载公开合集
async function loadCollections() {
  try {
    await collectionStore.fetchPublicCollections({
      page: 1,
      pageSize: 20,
      append: false
    });

    // 如果用户已登录，加载收藏状态
    if (userStore.isLoggedIn) {
      await collectionFavoriteStore.fetchFavoriteCollections();
    }
  } catch (error) {
    console.error('加载合集失败:', error);
  }
}

// 处理合集收藏/取消收藏
async function handleToggleFavorite(collection) {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录');
    return;
  }

  try {
    await collectionFavoriteStore.toggleFavorite(collection.id);
    ElMessage.success(
      collectionFavoriteStore.isFavorite(collection.id) ? '收藏成功' : '取消收藏成功'
    );
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败，请重试');
  }
}

// 加载更多合集
async function loadMoreCollections() {
  if (collectionsLoading.value || !hasMoreCollections.value) {
    return;
  }

  try {
    await collectionStore.loadMoreCollections();
  } catch (error) {
    console.error('加载更多合集失败:', error);
    ElMessage.error('加载失败，请稍后重试');
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 初始化语言状态
  languageStore.loadLearningLanguage();

  // 同步搜索框的值
  searchQuery.value = filters.value.search || '';

  // 从URL参数读取当前tab
  const tabParam = route.query.tab;
  if (tabParam === 'collections') {
    activeTab.value = 'collections';
  }

  await initializeData();


});
</script>

<style scoped>
.home-view {
  min-height: 100%;
  background-color: #f5f7fa;
  display: flex;
  position: relative;
}

/* 首页标题栏 */
.home-header {
  background-color: #ffffff;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-sizing: border-box;
}

.page-title {
  font-size: 1.25rem;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}



/* 侧边栏切换按钮 */
.sidebar-toggle-btn {
  flex-shrink: 0;
}

/* 侧边栏样式 */
.home-sidebar {
  width: 0;
  background-color: #ffffff;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  transition: width 0.3s;
  overflow: hidden;
  box-shadow: 0.125rem 0 0.5rem rgba(0, 0, 0, 0.1);
}

.home-sidebar.visible {
  width: 16rem;
}

/* 遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

/* 主内容区域容器 */
.home-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  margin-left: 0;
  transition: margin-left 0.3s;
  position: relative;
  width: 100%;
}



/* 主要内容区域 */
.home-main {
  flex: 1;
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding-top: 4rem;
}

/* 搜索栏样式 */
.search-bar {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  max-width: 600px;
}

/* 内容列表容器 */
.content-list-container {
  flex: 1;
}

/* Tab切换区域 */
.home-tabs {
  width: 100%;
}

.content-tabs {
  width: 100%;
}

.content-tabs :deep(.el-tabs__header) {
  margin-bottom: 1.5rem;
}

.content-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 1rem;
}

.content-tabs :deep(.el-tabs__item) {
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
}

.content-tabs :deep(.el-tabs__content) {
  padding: 0;
}



.mobile-layout .home-main {
  padding: 0.5rem;
  padding-top: 3.5rem;
}

.mobile-layout .page-title {
  font-size: 1.125rem;
}

.mobile-layout .home-tabs {
  width: 100%;
}

.mobile-layout .content-tabs :deep(.el-tabs__header) {
  margin-bottom: 1rem;
  padding: 0.125rem;
}

.mobile-layout .content-tabs :deep(.el-tabs__item) {
  font-size: 0.8rem;
  padding: 0 0.875rem;
  height: 2rem;
  line-height: 2rem;
}

/* 推荐内容样式 */
.recommended-wrapper {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
}

.recommend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0.75rem;
  border-bottom: 0.125rem solid #f0f2f5;
}

.recommend-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.recommend-controls .search-control {
  flex-shrink: 0;
}

.recommend-controls .search-input {
  width: 250px;
}

.recommend-controls .search-input :deep(.el-input__wrapper) {
  padding-right: 0;
}

.recommend-controls .search-button {
  border-radius: 0 4px 4px 0;
  border-left: none;
  margin-right: 1px;
}

.recommend-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 1.125rem;
  color: #409eff;
}

.change-level-btn {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
}

.no-level-tip {
  padding: 2rem 0;
  text-align: center;
  background: white;
  border-radius: 0.75rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.125rem;
  }

  .recommend-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .recommend-controls {
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
  }

  .recommend-controls .search-control {
    flex: 1;
  }

  .recommend-controls .search-input {
    width: 100%;
  }

  .recommend-controls .change-level-btn {
    flex-shrink: 0;
    white-space: nowrap;
  }

  .first-visit-dialog .action-buttons {
    flex-direction: column;
  }
}

/* 首次访问弹窗样式 */
.first-visit-dialog .welcome-content {
  padding: 1rem 0;
}

.first-visit-dialog .welcome-message {
  text-align: center;
  margin-bottom: 2rem;
}

.first-visit-dialog .welcome-text {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.first-visit-dialog .quick-setup {
  margin-bottom: 2rem;
}

.first-visit-dialog .action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 合集内容区域 */
.collections-content {
  padding: 0;
}

/* 搜索和筛选栏样式 */
.search-filter-bar {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
}

.search-section {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
}

.filter-section {
  flex-shrink: 0;
}

/* 手机端搜索筛选栏 */
.mobile-search-filter {
  flex-direction: column;
  gap: 12px;
}

.mobile-search-filter .search-section {
  flex: none;
  min-width: auto;
}

.mobile-search-filter .filter-section {
  flex: none;
  min-width: auto;
}

</style>
