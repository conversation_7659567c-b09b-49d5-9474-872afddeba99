// 确保Vue核心首先加载
import { createApp } from "vue";

// Element Plus 中文语言包
import ElementPlus from "element-plus";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";

// 按需引入Element Plus样式
import "@/styles/element-plus.scss";
import "@/assets/styles/element-overrides.css";

// 其他核心导入
import { createPinia } from "pinia";
import App from "./App.vue";
import router, { setupRouterGuards } from "./router";
// 节点组件改为按需加载，不在全局注册
// import { registerNodeComponents } from "@/components/nodes";
import "@/core";
import { registerSW } from "virtual:pwa-register";
import { hideInitialLoading } from "./utils/hideLoading";

// 创建应用实例
const app = createApp(App);

// 创建 Pinia 实例
const pinia = createPinia();

// 使用插件（确保顺序正确）
app.use(pinia);
app.use(router);
app.use(ElementPlus, { locale: zhCn });

// 设置路由守卫，传入 pinia 实例
setupRouterGuards(pinia);

// 认证拦截器已在httpClient中统一设置，无需单独设置

// 初始化应用
function initializeApp() {
  try {
    // 挂载应用
    app.mount("#app");

    console.log("应用初始化完成");
  } catch (error) {
    console.error("应用初始化失败:", error);
  } finally {
    // 无论成功还是失败，都隐藏loading
    hideInitialLoading();
  }
}

// 启动应用
initializeApp();

// 注册 Service Worker
registerSW({
  onNeedRefresh() {
    ElMessageBox.confirm("发现新版本，是否立即更新？", "更新提示", {
      confirmButtonText: "立即更新",
      cancelButtonText: "稍后再说",
      type: "info",
    })
      .then(() => {
        window.location.reload();
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "您可以稍后刷新页面以获取更新",
        });
      });
  },
  onOfflineReady() {
    ElMessage({
      message: "应用已准备好离线使用",
      type: "success",
      duration: 2000,
    });
  },
  onRegistered(registration) {
    console.log("Service Worker 注册成功:", registration);
  },
  onRegisterError(error) {
    console.error("Service Worker 注册失败:", error);
  },
});
