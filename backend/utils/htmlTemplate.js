const fs = require('fs');
const path = require('path');

// 获取资源文件路径
function getBuildAssets() {
  const isDev = process.env.NODE_ENV === 'development';
  
  if (isDev) {
    return { css: '', js: '/src/main.js' };
  }
  
  try {
    const indexHtmlPath = path.join(__dirname, '../../echo-lab/dist/index.html');
    const indexHtml = fs.readFileSync(indexHtmlPath, 'utf-8');
    
    const cssMatch = indexHtml.match(/<link[^>]*href="([^"]*\.css)"[^>]*>/);
    const jsMatch = indexHtml.match(/<script[^>]*src="([^"]*\.js)"[^>]*>/);
    
    return {
      css: cssMatch ? cssMatch[1] : '',
      js: jsMatch ? jsMatch[1] : '/src/main.js'
    };
  } catch (error) {
    console.error('读取构建资源失败:', error);
    return { css: '', js: '/src/main.js' };
  }
}

// 生成基础HTML模板
function generateBaseHTML(options = {}) {
  const {
    title = 'Echo Lab - 日语精听工具',
    description = 'Echo Lab 是一个高效的精听学习工具，帮助你通过重复听力快速提升语言理解力。',
    keywords = '日语,精听,听力训练,语言学习,日本语,N2,N3,N4,N5',
    ogUrl = 'https://echolab.club',
    ogImage = '',
    ssrData = null,
    appDataId = '',
    loadingText = '别着急，正在努力加载中...'
  } = options;

  const assets = getBuildAssets();

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/icons/logo-128.png" type="image/png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>${title}</title>
  <meta name="description" content="${description}">
  <meta name="keywords" content="${keywords}">
  
  <meta property="og:title" content="${title}">
  <meta property="og:description" content="${description}">
  <meta property="og:url" content="${ogUrl}">
  ${ogImage ? `<meta property="og:image" content="${ogImage}">` : ''}
  
  <!-- PWA支持 -->
  <meta name="theme-color" content="#ff0000" />
  <link rel="manifest" href="/manifest.webmanifest" />
  
  ${assets.css ? `<link rel="stylesheet" crossorigin href="${assets.css}">` : ''}
  
  <style>
    html { font-size: 16px; }
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #f8fafc;
      z-index: 9999;
    }
    .loading-text {
      margin-top: 2rem;
      font-size: 0.9rem;
      color: #64748b;
      letter-spacing: 0.03125rem;
    }
    .loader {
      height: 1rem;
      aspect-ratio: 4;
      --_g: no-repeat radial-gradient(farthest-side, red 90%, #0000);
      background: var(--_g) left, var(--_g) right;
      background-size: 25% 100%;
      display: grid;
    }
    .loader:before,
    .loader:after {
      content: "";
      height: inherit;
      aspect-ratio: 1;
      grid-area: 1/1;
      margin: auto;
      border-radius: 50%;
      transform-origin: -100% 50%;
      background: red;
      animation: l49 1s infinite linear;
    }
    .loader:after {
      transform-origin: 200% 50%;
      --s: -1;
      animation-delay: -0.5s;
    }
    @keyframes l49 {
      58%, 100% { transform: rotate(calc(var(--s, 1) * 1turn)); }
    }
  </style>
  
  <!-- 统计代码 -->
  <script type="text/javascript">
    if (location.hostname === "echolab.club" && localStorage.getItem("skipAnalytics") !== "true") {
      !(function (e, t, n, g, i) {
        (e[i] = e[i] || function () { (e[i].q = e[i].q || []).push(arguments); }),
        (n = t.createElement("script")), (tag = t.getElementsByTagName("script")[0]),
        (n.async = 1), (n.src = (("https:" == document.location.protocol ? "https://" : "http://") + g)),
        tag.parentNode.insertBefore(n, tag);
      })(window, document, "script", "assets.giocdn.com/2.1/gio.js", "gio");
      gio("init", "9c11d8e6f8077ecd", {});
      gio("send");
    }
  </script>
</head>
<body>
  <script>
    if (location.hostname === "echolab.club" && localStorage.getItem("skipAnalytics") !== "true") {
      !(function (c, b, d, a) {
        c[a] || (c[a] = {});
        c[a] = { pid: "dq55fcf310@8a659a0085b712a", endpoint: "https://dq55fcf310-default-cn.rum.aliyuncs.com", remoteConfig: { region: "cn-hangzhou" } };
        with (b) with (body) with (insertBefore(createElement("script"), firstChild))
          setAttribute("crossorigin", "", (src = d));
      })(window, document, "https://dq55fcf310-sdk.rum.aliyuncs.com/v2/browser-sdk.js", "__rum");
    }
  </script>
  
  ${ssrData ? `<script>window.__SSR_DATA__ = ${JSON.stringify(ssrData)};</script>` : ''}
  
  <div id="loading" class="loading-container">
    <div class="loader"></div>
    <div class="loading-text">${loadingText}</div>
  </div>
  <div id="app"${appDataId ? ` data-content-id="${appDataId}"` : ''}></div>
  <script type="module" crossorigin src="${assets.js}"></script>
</body>
</html>`;
}

module.exports = {
  generateBaseHTML,
  getBuildAssets
};