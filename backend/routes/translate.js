const express = require("express");
const router = express.Router();
const axios = require("axios");

// 阿里云百炼API配置
const DASHSCOPE_API_KEY = process.env.DASHSCOPE_API_KEY;
const DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";

// 语言代码到语言名称的映射
const LANGUAGE_NAMES = {
  "zh-CN": "简体中文",
  "zh-TW": "繁体中文",
  en: "英语",
  ja: "日语",
};

// 验证阿里云百炼API密钥
if (!DASHSCOPE_API_KEY) {
  console.warn("警告: 阿里云百炼API密钥未设置，翻译功能将不可用");
}

/**
 * 调用阿里云百炼API进行翻译
 * @param {Array} texts 文本数组，每个元素包含 {id, content, language}
 * @param {Array} targetLanguages 目标语言数组
 * @returns {Promise<Object>} 翻译结果，格式为 {textId: {lang: translation}}
 */
async function callDashScopeTranslate(texts, targetLanguages) {
  if (!DASHSCOPE_API_KEY) {
    throw new Error("阿里云百炼API密钥未配置");
  }

  if (texts.length === 0 || targetLanguages.length === 0) {
    return {};
  }

  // 构建翻译prompt
  const targetLanguageNames = targetLanguages
    .map((lang) => LANGUAGE_NAMES[lang] || lang)
    .join("、");

  // 构建文本列表
  const textList = texts
    .map((text) => `[${text.id}] ${text.content}`)
    .join("\n");

  const prompt = `请将以下文本翻译为${targetLanguageNames}，保持段落对应关系：

${textList}

重要：请严格按以下JSON格式返回，不要使用Markdown代码块格式（不要用\`\`\`json或\`\`\`），不要添加任何解释文字，只返回纯JSON：
{
${texts
  .map((text) => {
    const translations = targetLanguages
      .map((lang) => `    "${lang}": "翻译内容"`)
      .join(",\n");
    return `  "${text.id}": {\n${translations}\n  }`;
  })
  .join(",\n")}
}`;

  let allResults = {};

  try {
    console.log(
      `使用阿里云百炼翻译: [${targetLanguages.join(
        ", "
      )}], ${texts.length} 个文本`
    );

    const response = await axios.post(
      DASHSCOPE_API_URL,
      {
        model: "qwen-plus",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 8000,
        temperature: 0.1,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${DASHSCOPE_API_KEY}`,
        },
        timeout: 600000, // 10分钟超时
      }
    );

      if (response.data?.choices?.[0]?.message?.content) {
        let content = response.data.choices[0].message.content.trim();

      try {
        // 清理阿里云百炼返回的内容，移除可能的Markdown代码块标记和其他格式
        content = content.replace(/^```json\s*/i, "").replace(/\s*```$/i, "");
        content = content.replace(/^```\s*/i, "").replace(/\s*```$/i, "");
        content = content.replace(/^json\s*/i, ""); // 移除可能的 "json" 前缀
        content = content.replace(/^\s*{\s*$[\s\S]*^\s*}\s*$/m, content); // 确保只保留JSON部分
        content = content.trim();

        // 如果内容不是以 { 开头，尝试找到第一个 {
        if (!content.startsWith("{")) {
          const jsonStart = content.indexOf("{");
          if (jsonStart !== -1) {
            content = content.substring(jsonStart);
          }
        }

        // 如果内容不是以 } 结尾，尝试找到最后一个 }
        if (!content.endsWith("}")) {
          const jsonEnd = content.lastIndexOf("}");
          if (jsonEnd !== -1) {
            content = content.substring(0, jsonEnd + 1);
          }
        }

        console.log(
          "清理后的阿里云百炼响应内容:",
          content.substring(0, 200) + (content.length > 200 ? "..." : "")
        );

        // 尝试解析JSON响应
        const translationResult = JSON.parse(content);

        // 合并到总结果中
        allResults = translationResult;
      } catch (parseError) {
        console.error("阿里云百炼返回的JSON格式无效:", content);
        console.error("解析错误:", parseError.message);

        // 解析失败时，为这批文本返回空翻译
        texts.forEach((text) => {
          allResults[text.id] = {};
          targetLanguages.forEach((lang) => {
            allResults[text.id][lang] = "";
          });
        });
      }
    } else {
      console.error("阿里云百炼API返回格式异常:", response.data);
      throw new Error("阿里云百炼API返回格式异常");
    }
  } catch (error) {
    console.error("阿里云百炼翻译请求失败:", error.message);

    // 请求失败时，为这批文本返回空翻译
    texts.forEach((text) => {
      allResults[text.id] = {};
      targetLanguages.forEach((lang) => {
        allResults[text.id][lang] = "";
      });
    });
  }

  return allResults;
}

// 阿里云百炼翻译API
router.post("/", async (req, res) => {
  try {
    const { targetLanguages, texts } = req.body;

    console.log("收到阿里云百炼翻译请求:");
    console.log("目标语言:", targetLanguages);
    console.log("文本数量:", texts?.length || 0);

    // 验证请求参数
    if (
      !targetLanguages ||
      !Array.isArray(targetLanguages) ||
      targetLanguages.length === 0
    ) {
      return res.status(400).json({ error: "缺少目标语言参数或格式不正确" });
    }

    if (!texts || !Array.isArray(texts) || texts.length === 0) {
      return res.status(400).json({ error: "缺少文本参数或格式不正确" });
    }

    // 检查文本格式
    const hasInvalidText = texts.some(
      (text) => !text.id || !text.content
    );
    if (hasInvalidText) {
      return res.status(400).json({
        error: "文本格式不正确，每个文本必须包含 id 和 content",
      });
    }

    // 调用阿里云百炼翻译服务
    const dashScopeResults = await callDashScopeTranslate(texts, targetLanguages);

    // 将阿里云百炼结果转换为前端期望的格式
    const translationResults = texts.map((text) => ({
      id: text.id,
      content: text.content,
      translations: dashScopeResults[text.id] || {},
    }));

    // 返回翻译结果
    console.log(`阿里云百炼翻译完成，返回 ${translationResults.length} 个结果`);

    return res.json({
      success: true,
      translations: translationResults,
    });
  } catch (error) {
    console.error("阿里云百炼翻译请求处理失败:", error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;
