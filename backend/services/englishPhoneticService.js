/**
 * English Phonetic Transcription Service
 * 用于获取英语文本的音标标注
 * 基于日语Yahoo API的模式实现，使用阿里云百炼API生成音标
 */
const axios = require("axios");

// 阿里云百炼API配置
const DASHSCOPE_API_KEY = "sk-cf7938388ece44b18b9aa16c76d19401";
const DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";

// 验证阿里云百炼API密钥
if (!DASHSCOPE_API_KEY) {
  console.warn("警告: 阿里云百炼API密钥未设置，英语音标功能将不可用");
}



/**
 * 调用阿里云百炼API获取音标标注
 * @param {Array} segments - 需要标注的文本段落数组
 * @returns {Promise<Object>} - 标注结果
 */
async function callDashScopePhonetic(segments) {
  if (!DASHSCOPE_API_KEY) {
    throw new Error("阿里云百炼API密钥未配置");
  }

  if (segments.length === 0) {
    return {};
  }

  try {
    console.log(
      `使用阿里云百炼(qwen-plus)生成英语音标，段落数: ${segments.length}，优先保证准确性`
    );

    // 构建文本列表
    const textList = segments
      .map((segment) => `[${segment.id}] ${segment.content}`)
      .join("\n");

    const prompt = `You are a professional phonetic transcription expert. Generate ACCURATE American English IPA phonetic transcription for the following English texts. Use General American pronunciation standards. ACCURACY IS CRITICAL - double-check each transcription.

IMPORTANT: Break down text into individual words and punctuation for better readability. Each word should have its own IPA transcription.

${textList}

Return ONLY valid JSON (no markdown, no explanations):
{
${segments
  .map((segment) => {
    return `  "${segment.id}": {
    "reading": "complete_ipa_transcription_of_full_text",
    "characters": [
      {"char": "individual_word_or_punctuation", "reading": "corresponding_ipa_or_same", "isEdited": false}
    ]
  }`;
  })
  .join(",\n")}
}

CRITICAL RULES:
1. Split text into individual words, spaces, and punctuation marks
2. Each English word gets its own accurate American IPA transcription
3. Spaces and punctuation: keep exactly as-is (char = reading)
4. Use standard American IPA symbols (e.g., /ɑ/ not /ɒ/, /ɜr/ not /ɜː/)
5. Set all "isEdited" to false
6. NEVER group multiple words together - each word is separate

Example for "Hello world! It's great.":
{
  "example": {
    "reading": "həˈloʊ wɜːrld ɪts ɡreɪt",
    "characters": [
      {"char": "Hello", "reading": "həˈloʊ", "isEdited": false},
      {"char": " ", "reading": " ", "isEdited": false},
      {"char": "world", "reading": "wɜːrld", "isEdited": false},
      {"char": "!", "reading": "!", "isEdited": false},
      {"char": " ", "reading": " ", "isEdited": false},
      {"char": "It's", "reading": "ɪts", "isEdited": false},
      {"char": " ", "reading": " ", "isEdited": false},
      {"char": "great", "reading": "ɡreɪt", "isEdited": false},
      {"char": ".", "reading": ".", "isEdited": false}
    ]
  }
}`;

    const response = await axios.post(
      DASHSCOPE_API_URL,
      {
        model: "qwen-plus",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 16000, // 设置更大的输出长度，确保完整返回
        temperature: 0.1,  // 低温度确保准确性和一致性
        stream: false,     // 对于JSON格式输出，不使用流式
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${DASHSCOPE_API_KEY}`,
        },
        timeout: 120000, // 2分钟超时，qwen-plus需要更多时间但更准确
      }
    );

    if (response.data?.choices?.[0]?.message?.content) {
      let content = response.data.choices[0].message.content.trim();

      try {
        // 清理可能的markdown代码块标记
        if (content.startsWith('```json')) {
          content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (content.startsWith('```')) {
          content = content.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // 尝试解析JSON
        const result = JSON.parse(content);

        console.log("=== 阿里云百炼原始返回结果 ===");
        console.log(JSON.stringify(result, null, 2));

        // 直接使用阿里云百炼返回的结果，不需要任何修正

        console.log("=== 最终返回结果 ===");
        console.log(JSON.stringify(result, null, 2));
        console.log("阿里云百炼音标生成成功");
        return result;
      } catch (parseError) {
        console.error("解析阿里云百炼响应失败:", parseError.message);
        console.error("响应内容:", content);
        throw new Error("音标API返回格式不正确");
      }
    } else {
      console.error("阿里云百炼API返回格式不正确:", response.data);
      throw new Error("音标API返回格式不正确");
    }
  } catch (error) {
    console.error("调用阿里云百炼API失败:", error.message);
    if (error.response) {
      console.error("错误响应:", error.response.data);
      console.error("状态码:", error.response.status);
    }
    throw error;
  }
}

/**
 * 批量获取音标标注
 * @param {Array} segments - 文本段落数组，每个元素包含id和content
 * @returns {Promise<Object>} - 标注结果，格式为 {id: 标注结果}
 */
async function batchGetPhonetics(segments) {
  try {
    console.log(`批量获取音标标注，段落数: ${segments.length}`);

    // 创建结果对象
    const results = {};

    // 过滤空内容
    const validSegments = segments.filter(segment =>
      segment.content && segment.content.trim() !== ""
    );

    if (validSegments.length === 0) {
      // 如果没有有效内容，返回空结果
      segments.forEach(segment => {
        results[segment.id] = {
          reading: segment.content || "",
          characters: [],
        };
      });
      return results;
    }

    // 限制单次处理的段落数量，避免请求过大
    const BATCH_SIZE = 10; // 每次最多处理10个段落
    const batches = [];
    for (let i = 0; i < validSegments.length; i += BATCH_SIZE) {
      batches.push(validSegments.slice(i, i + BATCH_SIZE));
    }

    try {
      // 并行处理多个批次
      const batchPromises = batches.map(batch => callDashScopePhonetic(batch));
      const batchResults = await Promise.all(batchPromises);

      // 合并所有批次的结果
      const dashScopeResults = {};
      batchResults.forEach(batchResult => {
        Object.assign(dashScopeResults, batchResult);
      });

      // 处理结果
      segments.forEach(segment => {
        if (!segment.content || segment.content.trim() === "") {
          results[segment.id] = {
            reading: segment.content || "",
            characters: [],
          };
        } else if (dashScopeResults[segment.id]) {
          // 使用阿里云百炼返回的结果
          results[segment.id] = dashScopeResults[segment.id];
        } else {
          // 如果阿里云百炼没有返回结果，使用原文
          results[segment.id] = {
            reading: segment.content,
            characters: [],
          };
        }
      });

    } catch (error) {
      console.error("阿里云百炼音标生成失败:", error.message);
      // 失败时返回原文
      segments.forEach(segment => {
        results[segment.id] = {
          reading: segment.content || "",
          characters: [],
        };
      });
    }

    return results;
  } catch (error) {
    console.error("批量获取音标标注失败:", error.message);
    throw error;
  }
}

module.exports = {
  batchGetPhonetics,
};
