/**
 * 关键词提取服务
 * 使用阿里云百炼API智能提取内容关键词
 */
const axios = require("axios");

// 阿里云百炼API配置
const DASHSCOPE_API_KEY = "sk-cf7938388ece44b18b9aa16c76d19401";
const DASHSCOPE_API_URL =
  "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";

/**
 * 从文本内容中提取关键词
 * @param {string} textContent - 文本内容
 * @returns {Promise<Array>} - 关键词数组（中文）
 */
async function extractKeywords(textContent) {
  if (!DASHSCOPE_API_KEY) {
    throw new Error("阿里云百炼API密钥未配置");
  }

  if (!textContent || textContent.trim().length === 0) {
    return [];
  }

  try {
    console.log(`使用阿里云百炼提取关键词，文本长度: ${textContent.length}`);

    const prompt = buildPrompt(textContent);

    const response = await axios.post(
      DASHSCOPE_API_URL,
      {
        model: "qwen-plus",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 4000,
        temperature: 0.1,
        stream: false,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${DASHSCOPE_API_KEY}`,
        },
        timeout: 60000,
      }
    );

    if (response.data?.choices?.[0]?.message?.content) {
      let content = response.data.choices[0].message.content.trim();

      // 清理可能的markdown代码块标记
      if (content.startsWith("```json")) {
        content = content.replace(/^```json\s*/, "").replace(/\s*```$/, "");
      } else if (content.startsWith("```")) {
        content = content.replace(/^```\s*/, "").replace(/\s*```$/, "");
      }

      try {
        const result = JSON.parse(content);
        return result.keywords || [];
      } catch (parseError) {
        console.error("解析关键词结果失败:", parseError.message);
        console.error("响应内容:", content);
        return [];
      }
    }

    return [];
  } catch (error) {
    console.error("提取关键词失败:", error.message);
    return [];
  }
}

/**
 * 构建提取关键词的提示词
 */
function buildPrompt(textContent) {
  return `你是一个网站SEO专家。请从以下文本中提取适合搜索引擎优化的中文关键词。

文本内容：
${textContent}

要求：
1. 提取用户可能搜索的热门中文关键词
2. 关键词应该是具体的、有搜索价值的词汇
3. 所有关键词必须翻译成中文，方便中文母语者搜索
4. 关键词长度：2-6个中文字符
5. 返回最多6个关键词

返回格式：
{
  "keywords": ["关键词1", "关键词2"]
}`;
}

module.exports = {
  extractKeywords,
};
